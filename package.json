{"name": "quantitative_trading_front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:mock": "MOCK=ON vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@tailwindcss/vite": "^4.1.6", "ahooks": "^3.8.4", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lightweight-charts": "^5.0.6", "lucide-react": "^0.511.0", "mockjs": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.5.1", "react-router": "^7.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "zod": "^3.25.67", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@iconify-json/cryptocurrency-color": "^1.2.2", "@iconify/tailwind4": "^1.0.6", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-mock": "^3.0.2"}}