export type Coin = 'BTC' | 'ETH' | 'ADA' | 'SOL' | 'DOT' | 'LINK' | 'XRP' | 'BCH' | 'LTC' | 'XMR' | 'XLM' | 'XEM' | 'XVG' | 'XZC' | 'XRP' | 'BCH' | 'LTC' | 'XMR' | 'XLM' | 'XEM' | 'XVG' | 'XZC'

export type KLineDataMap = Record<string, KLineData[]>

export interface KLineData {
  time: number
  open: number
  high: number
  low: number
  close: number
}

export interface KLineUnionData extends KLineData {
  ema5?: number
  ema10?: number
  ema20?: number
  sma5?: number
  sma10?: number
  sma20?: number
}

export interface User {
  username?: string | null
  email: string
}
