import { useTranslation } from 'react-i18next'
import { useRequest } from 'ahooks'
import { fetchDealOrder } from '@/api'
import { cn } from '@/lib/utils'
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Loading from '@/components/loading'

// Define interfaces for the API response data structure
interface TradeDetail {
  cost: string
  profit: string
  rate: string
  scroll_long_cost?: string
  scroll_short_cost?: string
  scroll_tota_cost?: string
}

interface DailyTradeData {
  long?: TradeDetail
  short?: TradeDetail
  total?: TradeDetail
}

export default function HistoryOrder() {
  const { t } = useTranslation()
  // Let useRequest infer the type based on fetchDealOrder's return type
  const { data: dealOrderResponse, loading, error } = useRequest(fetchDealOrder) // Use inferred type, renamed to dealOrderResponse

  if (error) {
    return <div className="flex items-center justify-center h-full text-red-500">{t('Error loading history data.')}</div>
  }

  // Access the actual daily data which is the 'data' property of the response
  // Use optional chaining and nullish coalescing
  const rawData = dealOrderResponse?.data || {}

  // If rawData is null or undefined, treat it as an empty object for key filtering
  const dataKeys = Object.keys(rawData).filter((key) => key !== 'arerage_rate') // Object.keys works on {} too
  const dates = dataKeys.sort((a, b) => b.localeCompare(a)) // Sort dates in descending order
  const averageRate = rawData.arerage_rate || '0'

  return (
    <Loading spinning={loading}>
      <div className="p-4 overflow-auto h-full">
        <div className="mb-6">
          <div className="text-lg font-semibold mb-2">历史平均收益率</div>
          <div className={`text-2xl font-bold ${parseFloat(averageRate) >= 1 ? 'text-green-500' : parseFloat(averageRate) < 0 ? 'text-red-500' : ''}`}>{averageRate}%</div>
        </div>
        {!loading && dates.length === 0 ? (
          <div className="text-center text-muted-foreground">{t('No historical trade data available.')}</div>
        ) : (
          <div className="space-y-6">
            {' '}
            {/* Add space between daily tables */}
            {dates.map((date) => {
              // Access data for the specific date, safely using optional chaining
              // rawData is likely inferred as any or {} here, we trust our filtering and runtime check
              const dayTrades = rawData[date] as DailyTradeData | undefined

              if (!dayTrades) return null // Should not happen if date comes from filtered keys, but for safety

              const { long, short, total } = dayTrades

              return (
                <div key={date} className="bg-background rounded-lg p-4">
                  <h3 className="text-xl font-semibold mb-2">{date}</h3>
                  <Table>
                    {/* <TableCaption>{date}</TableCaption> */}
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-zinc-400">类型</TableHead>
                        <TableHead className="text-right text-zinc-400">利润</TableHead>
                        <TableHead className="text-right text-zinc-400">利润率</TableHead>
                        <TableHead className="text-right text-zinc-400">成本</TableHead>
                        <TableHead className="text-right text-zinc-400">滚动成本</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {long && (
                        <TableRow>
                          <TableCell className="">做多</TableCell>
                          <TableCell className="text-right">{long.profit}</TableCell>
                          <TableCell className="text-right">{long.rate}%</TableCell>
                          <TableCell className="text-right">{long.cost}</TableCell>
                          <TableCell className="text-right">{long.scroll_long_cost}</TableCell>
                        </TableRow>
                      )}
                      {short && (
                        <TableRow>
                          <TableCell className="">做空</TableCell>
                          <TableCell className="text-right">{short.profit}</TableCell>
                          <TableCell className="text-right">{short.rate}%</TableCell>
                          <TableCell className="text-right">{short.cost}</TableCell>
                          <TableCell className="text-right">{short.scroll_short_cost}</TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                    <TableFooter>
                      <TableRow>
                        <TableCell className="">总计</TableCell>
                        <TableCell className="text-right">{total?.profit}</TableCell>
                        <TableCell className={cn('text-right', { 'text-loss': parseFloat(total?.rate || '') < 0, 'text-profit': parseFloat(total?.rate || '') >= 1 })}>{total?.rate}%</TableCell>
                        <TableCell className="text-right">{total?.cost}</TableCell>
                        <TableCell className="text-right">{total?.scroll_tota_cost}</TableCell>
                      </TableRow>
                    </TableFooter>
                  </Table>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </Loading>
  )
}
