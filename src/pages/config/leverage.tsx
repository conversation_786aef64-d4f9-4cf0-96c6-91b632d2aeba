import { Card, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

export default function Leverage({ data = {} }) {
  const { long = {}, short = {} } = data as any
  return (
    <Card className="gap-2">
      <CardHeader>
        <CardTitle className="text-lg">杠杆倍数</CardTitle>
      </CardHeader>
      <CardContent className="flex gap-8">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead></TableHead>
              {Object.keys(long).map((coin) => (
                <TableHead key={coin}>{coin}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>做多</TableCell>
              {Object.keys(long).map((coin) => (
                <TableCell key={coin} className="text-center">
                  {long[coin]}
                </TableCell>
              ))}
            </TableRow>
            <TableRow>
              <TableCell>做空</TableCell>
              {Object.keys(short).map((coin) => (
                <TableCell key={coin} className="text-center">
                  {short[coin]}
                </TableCell>
              ))}
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
