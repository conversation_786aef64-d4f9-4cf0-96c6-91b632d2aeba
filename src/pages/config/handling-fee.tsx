import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const platforms = {
  binance: '币安',
  bitget: 'Bitget',
}

export default function HandlingFee({ data = {} }) {
  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle>手续费</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="flex gap-8">
          {Object.entries(data).map(([platform, { spot, future }]: [string, any]) => (
            <li key={platform}>
              <h4 className="mb-1">{platforms[platform as keyof typeof platforms]}</h4>
              <div className="flex flex-col gap-1 text-sm">
                <div>
                  <span className="text-muted-foreground">现货</span>
                  <span className="ml-1 text-lg">{spot || '--'}</span>
                  <span className="ml-0.5 text-sm text-muted-foreground">%</span>
                </div>
                <div>
                  <span className="text-muted-foreground">期货</span>
                  <span className="ml-1 text-lg">{future || '--'}</span>
                  <span className="ml-0.5 text-sm text-muted-foreground">%</span>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
