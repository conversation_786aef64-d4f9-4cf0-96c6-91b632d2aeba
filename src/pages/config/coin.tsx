import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const icons = {
  ADA: <span className="icon-[cryptocurrency-color--ada] w-4 h-4" />,
  BTC: <span className="icon-[cryptocurrency-color--btc] w-4 h-4" />,
  RENDER: <span className="icon-[cryptocurrency-color--ren] w-4 h-4" />,
  SOL: <span className="icon-[cryptocurrency-color--sol] w-4 h-4" />,
  XRP: <span className="icon-[cryptocurrency-color--xrp] w-4 h-4" />,
  ETH: <span className="icon-[cryptocurrency-color--eth] w-4 h-4" />,
}

export default function Coin({ data = {} }) {
  const { buy_in: buyIn = [], kline = [], sell_out: sellOut = [] } = data as any

  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle>代币配置</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="flex flex-col gap-2">
          {[
            { title: '允许买入', list: buyIn },
            { title: '允许卖出', list: sellOut },
            { title: '拉取K线', list: kline },
          ].map(({ title, list }) => (
            <li className="flex items-center gap-2">
              <h3 className="shrink-0 text-sm text-muted-foreground">{title}</h3>
              <ul className="flex items-center gap-1">
                {list.map((item: string) => (
                  <li key={item} className="flex items-center justify-center">
                    <Badge variant="secondary">
                      {icons[item as keyof typeof icons]} {item}
                    </Badge>
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
