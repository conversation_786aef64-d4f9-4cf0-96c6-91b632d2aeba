import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function HandlingTime({ data = {} }) {
  // "continue_buy_interval": 1,
  // "double_profit": 12,
  // "max_profit": 8,
  // "min_profit": 4,
  // "preservation": 48,
  // "stop_loss": 336
  const { continue_buy_interval, max_profit, min_profit, preservation, stop_loss } = data as any
  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle>持仓配置</CardTitle>
        <CardDescription className="text-xs">单位：时</CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="flex gap-8 text-sm">
          {[
            [
              { title: '达到期望利润就卖出', value: max_profit },
              { title: '挣点是点', value: min_profit },
            ],
            [
              { title: '保本卖出时间', value: preservation },
              { title: '止损时间', value: stop_loss },
              { title: '再次买入间隔', value: continue_buy_interval },
            ],
          ].map((item, index) => (
            <li key={index}>
              <ul className="flex flex-col gap-1">
                {item.map(({ title, value }) => (
                  <li className="flex items-center gap-2">
                    <h3 className="shrink-0 text-muted-foreground">{title}</h3>
                    <p className="text-lg">{value}</p>
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
