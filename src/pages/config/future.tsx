import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function Future({ data = {} }) {
  const { beaker_count = 0, long_amount_rate = 0, stop_loss = 0, stop_profit = 0, total_amount_expand_rate = 0 } = data as any

  // "beaker_count": "累计买入次数",  # 累计几次买入,没有卖出,就熔断。
  //       "stop_profit": "止盈倍数",  # 超过 2.5 倍最大利润就止盈
  //       "stop_loss": "止损倍数",  # 超过 3 倍最大利润就止损
  //       "long_amount_rate": "开多占全部资金的比例",  # 开多的资金占全部资金的比例。省下的就是开空的
  //       "total_amount_expand_rate": "资金膨胀倍数",

  return (
    <Card className="gap-4">
      <CardHeader>
        <CardTitle>均线策略配置</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="flex gap-8 text-sm">
          {[
            [
              { title: '累计买入次数', value: beaker_count },
              { title: '止盈倍数', value: stop_profit },
              { title: '止损倍数', value: stop_loss },
            ],
            [
              { title: '开多占全部资金的比例', value: long_amount_rate, unit: '%' },
              { title: '资金膨胀倍数', value: total_amount_expand_rate, unit: '倍' },
            ],
          ].map((item, index) => (
            <li key={index}>
              <ul className="flex flex-col gap-1">
                {item.map(({ title, value, unit }: any) => (
                  <li className="flex items-center gap-2">
                    <h3 className="shrink-0 text-muted-foreground">{title}</h3>
                    <p className="text-lg">
                      {value} <span className="text-muted-foreground text-xs">{unit || ''}</span>
                    </p>
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
