import { useRequest } from 'ahooks'
import { fetchConfig } from '@/api'
import Loading from '@/components/loading'
import Coin from './coin'
import Future from './future'
import HandlingFee from './handling-fee'
import HandlingTime from './handling-time'
import Leverage from './leverage'
import Signal from './signal'

export default function Config() {
  const { loading, data } = useRequest(fetchConfig, {
    // cacheKey: 'fetchConfig',
  })

  const { COIN_LIST = {}, FUTURE_CONFIG = {}, HANDING_FEE = {}, HANDING_TIME = {}, LEVERAGE = {}, SINGLE = {} } = data?.data || {}

  return (
    <Loading spinning={loading} wrapperClassName="h-full">
      <div className="flex flex-row flex-wrap gap-4 p-4">
        <Coin data={COIN_LIST} />
        <Future data={FUTURE_CONFIG} />
        <HandlingFee data={HANDING_FEE} />
        <HandlingTime data={HANDING_TIME} />
        <Leverage data={LEVERAGE} />
        <Signal data={SINGLE} />
      </div>
    </Loading>
  )
}
