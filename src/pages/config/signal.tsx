import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

export default function Signal({ data = {} }) {
  const { long = {}, short = {} } = data as any
  return (
    <Card className="gap-2">
      <CardHeader>
        <CardTitle className="text-lg">置信度比例</CardTitle>
        <CardDescription className="text-xs">数字越小越严格</CardDescription>
      </CardHeader>
      <CardContent className="flex gap-8">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead></TableHead>
              {Object.keys(long).map((signal) => (
                <TableHead key={signal}>{signal}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>做多</TableCell>
              {Object.keys(long).map((signal) => (
                <TableCell key={signal} className="text-center">
                  {long[signal]}
                </TableCell>
              ))}
            </TableRow>
            <TableRow>
              <TableCell>做空</TableCell>
              {Object.keys(short).map((signal) => (
                <TableCell key={signal} className="text-center">
                  {short[signal]}
                </TableCell>
              ))}
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
