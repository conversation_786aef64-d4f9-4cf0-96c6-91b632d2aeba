import { useEffect, useState } from 'react'
import { useRequest } from 'ahooks'
import { RefreshCcw } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import usePositionStore from '@/store/position'
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import Loading from '@/components/loading'

const icons = {
  ADA: <span className="icon-[cryptocurrency-color--ada] w-4 h-4" />,
  BTC: <span className="icon-[cryptocurrency-color--btc] w-4 h-4" />,
  RENDER: <span className="icon-[cryptocurrency-color--ren] w-4 h-4" />,
  SOL: <span className="icon-[cryptocurrency-color--sol] w-4 h-4" />,
  XRP: <span className="icon-[cryptocurrency-color--xrp] w-4 h-4" />,
}

export default function Position() {
  const { t } = useTranslation()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const positionMap = usePositionStore((state) => state.positionMap)
  const fetchPosition = usePositionStore((state) => state.fetchPosition)
  const setCurrentCoin = usePositionStore((state) => state.setCurrentCoin)
  const { loading, refresh } = useRequest(fetchPosition)

  useEffect(() => {
    if (positionMap) {
      setExpandedItems(Object.keys(positionMap))
    }
  }, [positionMap])

  return (
    <Loading spinning={loading} wrapperClassName="h-full">
      <div className="h-full flex flex-col pt-4">
        <div className="flex items-center justify-between mb-4 px-4">
          <h2 className="text-2xl font-bold">{t('common.position')}</h2>
          <h5 className="text-sm font-medium">可用余额: {positionMap?.usdt_free || 0}</h5>
          <Button variant="ghost" onClick={refresh} aria-label="刷新持仓">
            <RefreshCcw className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto px-4">
          <Accordion type="multiple" value={expandedItems} onValueChange={setExpandedItems}>
            {Object.entries(positionMap)
              .filter(([key]) => key !== 'usdt_free')
              .map(([coinCode, detail]: any) => {
                const {
                  long = [] as any,
                  long_stop_loss = [] as any,
                  long_preservation = [] as any,
                  short = [] as any,
                  short_stop_loss = [] as any,
                  short_preservation = [] as any,
                  long_min_rate,
                  long_max_rate,
                  short_min_rate,
                  short_max_rate,
                  long_stop_profit,
                  short_stop_profit,
                  long_threshold_price,
                  short_threshold_price,
                } = detail
                return (
                  <AccordionItem key={coinCode} value={coinCode}>
                    <div className="flex items-center justify-between px-4 py-2">
                      <span className="inline-flex items-center gap-2 cursor-pointer hover:text-primary transition-colors text-xl font-bold" onClick={() => setCurrentCoin(coinCode)}>
                        {icons[coinCode as keyof typeof icons]}
                        {coinCode}
                      </span>
                      <AccordionTrigger className="w-auto p-0 hover:no-underline" />
                    </div>
                    <AccordionContent>
                      {long.length > 0 && (
                        <div>
                          <h4 className="text-base font-normal mb-2">
                            <span className="font-bold">多单</span>
                            <span className="text-sm font-normal text-muted-foreground ml-2">
                              ({long_min_rate}% ~ {long_max_rate}%)
                            </span>
                            <div className="text-sm text-muted-foreground mt-1 flex flex-col">
                              <div>
                                <span className="text-blue-500">止盈: {long_stop_profit}%</span>
                                <span className="mx-2">|</span>
                                <span className="text-blue-500">阈值: {long_threshold_price}</span>
                              </div>
                            </div>
                          </h4>
                          <ul className="space-y-2">
                            {long.map(({ amount, profit, profit_rate: rate }: any, i: number) => (
                              <li key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                                <div className="flex flex-col">
                                  <span className="text-sm text-muted-foreground">买入金额</span>
                                  <span className="font-medium">{amount}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">利润</span>
                                  <span className={`font-medium ${Number(profit) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{profit}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">盈亏比例</span>
                                  <span className={`font-medium ${Number(rate) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{rate}%</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {long_preservation.length > 0 && (
                        <div className="mt-4">
                          <h4 className="text-base font-normal mb-2">
                            <span className="font-bold">多单保本</span>
                          </h4>
                          <ul className="space-y-2">
                            {long_preservation.map(({ amount, profit, profit_rate: rate }: any, i: number) => (
                              <li key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                                <div className="flex flex-col">
                                  <span className="text-sm text-muted-foreground">买入金额</span>
                                  <span className="font-medium">{amount}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">利润</span>
                                  <span className={`font-medium ${Number(profit) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{profit}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">盈亏比例</span>
                                  <span className={`font-medium ${Number(rate) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{rate}%</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {long_stop_loss.length > 0 && (
                        <div className="mt-4">
                          <h4 className="text-base font-normal mb-2">
                            <span className="font-bold">多单止损</span>
                          </h4>
                          <ul className="space-y-2">
                            {long_stop_loss.map(({ stop_loss_price, loss_rate, loss_distance }: any, i: number) => (
                              <li key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                                <div className="flex flex-col">
                                  <span className="text-sm text-muted-foreground">止损金额</span>
                                  <span className="font-medium">{stop_loss_price}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">亏损比例</span>
                                  <span className={`font-medium ${Number(loss_rate) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{loss_rate}%</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">止损距离</span>
                                  <span className={`font-medium ${Number(loss_distance) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{loss_distance}%</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {long.length > 0 && short.length > 0 && <Separator className="my-4" />}
                      {short.length > 0 && (
                        <div>
                          <h4 className="text-base font-normal mb-2">
                            <span className="font-bold">空单</span>
                            <span className="text-sm font-normal text-muted-foreground ml-2">
                              ({short_min_rate}% ~ {short_max_rate}%)
                            </span>
                            <div className="text-sm text-muted-foreground mt-1 flex flex-col">
                              <div>
                                <span className="text-blue-500">止盈: {short_stop_profit}%</span>
                                <span className="mx-2">|</span>
                                <span className="text-blue-500">阈值: {short_threshold_price}</span>
                              </div>
                            </div>
                          </h4>
                          <ul className="space-y-2">
                            {short.map(({ amount, profit, profit_rate: rate }: any, i: number) => (
                              <li key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                                <div className="flex flex-col">
                                  <span className="text-sm text-muted-foreground">买入金额</span>
                                  <span className="font-medium">{amount}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">利润</span>
                                  <span className={`font-medium ${Number(profit) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{profit}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">盈亏比例</span>
                                  <span className={`font-medium ${Number(rate) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{rate}%</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {short_preservation.length > 0 && (
                        <div className="mt-4">
                          <h4 className="text-base font-normal mb-2">
                            <span className="font-bold">空单保本</span>
                          </h4>
                          <ul className="space-y-2">
                            {short_preservation.map(({ amount, profit, profit_rate: rate }: any, i: number) => (
                              <li key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                                <div className="flex flex-col">
                                  <span className="text-sm text-muted-foreground">买入金额</span>
                                  <span className="font-medium">{amount}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">利润</span>
                                  <span className={`font-medium ${Number(profit) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{profit}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">盈亏比例</span>
                                  <span className={`font-medium ${Number(rate) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{rate}%</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {short_stop_loss.length > 0 && (
                        <div className="mt-4">
                          <h4 className="text-base font-normal mb-2">
                            <span className="font-bold">空单止损</span>
                          </h4>
                          <ul className="space-y-2">
                            {short_stop_loss.map(({ stop_loss_price, loss_rate, loss_distance }: any, i: number) => (
                              <li key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded-lg">
                                <div className="flex flex-col">
                                  <span className="text-sm text-muted-foreground">止损金额</span>
                                  <span className="font-medium">{stop_loss_price}</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">亏损比例</span>
                                  <span className={`font-medium ${Number(loss_rate) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{loss_rate}%</span>
                                </div>
                                <div className="flex flex-col items-end">
                                  <span className="text-sm text-muted-foreground">止损距离</span>
                                  <span className={`font-medium ${Number(loss_distance) >= 0 ? 'text-green-500' : 'text-red-500'}`}>{loss_distance}%</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                    </AccordionContent>
                  </AccordionItem>
                )
              })}
          </Accordion>
        </div>
      </div>
    </Loading>
  )
}
