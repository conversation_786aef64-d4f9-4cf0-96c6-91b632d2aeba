import { useEffect, useRef } from 'react'
import { CandlestickSeries, createChart, createSeriesMarkers, type ChartOptions, type DeepPartial, type IChartApi, type SeriesMarker, type Time, LineStyle, LineSeries } from 'lightweight-charts'
import { useMount, useRequest } from 'ahooks'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { fetchTrade } from '@/api'
import Loading from '@/components/loading'
import usePositionStore from '@/store/position'

dayjs.extend(utc)

export default function Chart() {
  const markersRef = useRef<SeriesMarker<Time>[]>([])
  const chartElRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const seriesRef = useRef<any>(null)
  const seriesMarkersRef = useRef<any>(null)
  const ema5Ref = useRef<any>(null)
  const ema10Ref = useRef<any>(null)
  const ema20Ref = useRef<any>(null)
  const sma5Ref = useRef<any>(null)
  const sma10Ref = useRef<any>(null)
  const sma20Ref = useRef<any>(null)

  const currentCoin = usePositionStore((state) => state.currentCoin)
  const getKLineData = usePositionStore((state) => state.getKLineData)
  const fetchKLineData = usePositionStore((state) => state.fetchKLineData)

  const { loading, run: runFetchKLineData } = useRequest(fetchKLineData, {
    manual: true,
    onSuccess() {
      runFetchTrade()
      const klineData: any[] = []
      const ema5Data: any[] = []
      const ema10Data: any[] = []
      const ema20Data: any[] = []
      const sma5Data: any[] = []
      const sma10Data: any[] = []
      const sma20Data: any[] = []

      getKLineData(currentCoin!).forEach(({ close, open, high, low, time, ema5, ema10, ema20, sma5, sma10, sma20 }: any) => {
        klineData.push({ time, open, close, high, low })
        ema5Data.push({ time, value: ema5 })
        ema10Data.push({ time, value: ema10 })
        ema20Data.push({ time, value: ema20 })
        sma5Data.push({ time, value: sma5 })
        sma10Data.push({ time, value: sma10 })
        sma20Data.push({ time, value: sma20 })
      })

      seriesRef.current.setData(klineData)
      ema5Ref.current?.setData(ema5Data)
      ema10Ref.current?.setData(ema10Data)
      ema20Ref.current?.setData(ema20Data)
      sma5Ref.current?.setData(sma5Data)
      sma10Ref.current?.setData(sma10Data)
      sma20Ref.current?.setData(sma20Data)
    },
  })

  const { run: runFetchTrade } = useRequest(() => fetchTrade({ symbol: currentCoin! }), {
    refreshDeps: [currentCoin],
    manual: true,
    onSuccess(result) {
      if (result?.code === 200 && result.data) {
        const markers: SeriesMarker<Time>[] = []
        const tradesByTime: { [key: number]: { side: string; id: any; type: '开' | '平' }[] } = {}

        result.data.forEach(({ buy_in_time: buyInTime, sell_out_time: sellOutTime, side, id }: any) => {
          const buyTime = dayjs.utc(buyInTime).valueOf() / 1000
          const sellTime = sellOutTime ? dayjs.utc(sellOutTime).valueOf() / 1000 : null

          if (!tradesByTime[buyTime]) tradesByTime[buyTime] = []
          tradesByTime[buyTime].push({ side, id, type: '开' })

          if (sellTime !== null) {
            if (!tradesByTime[sellTime]) tradesByTime[sellTime] = []
            tradesByTime[sellTime].push({ side, id, type: '平' })
          }
        })

        Object.entries(tradesByTime).forEach(([timeStr, trades]) => {
          const time = parseFloat(timeStr)
          const combinedText = trades.map((trade) => `${trade.id}-${trade.type}${trade.side === 'long' ? '多' : '空'}`).join('\n')
          const isLongTrade = trades.some((trade) => trade.side === 'long')
          const color = isLongTrade ? '#e91e63' : '#2196F3'

          const klineItem = getKLineData(currentCoin!)?.find((item: any) => item.time === time)

          let position: SeriesMarker<Time>['position'] = 'aboveBar'

          if (klineItem) {
            const { low, ema5, ema10, ema20, sma5, sma10, sma20 } = klineItem
            const movingAverages = [ema5, ema10, ema20, sma5, sma10, sma20].filter((val) => val !== undefined && val !== null)

            const overlapsIfBelow = movingAverages.some((ma) => ma !== undefined && ma !== null && ma < low)

            if (!overlapsIfBelow) {
              position = 'belowBar'
            } else {
              position = 'aboveBar'
            }
          }

          markers.push({
            time: time as Time,
            position: position,
            color: color,
            shape: position === 'aboveBar' ? 'arrowDown' : 'arrowUp',
            text: combinedText,
          })
        })

        markersRef.current = markers
        const visibleRange = chartRef.current?.timeScale().getVisibleRange()
        if (visibleRange && seriesMarkersRef.current) {
          const { from, to } = visibleRange
          const visibleMarkers = markersRef.current.filter(({ time }) => time >= from && time <= to)
          seriesMarkersRef.current.setMarkers(visibleMarkers)
        }
      }
    },
  })

  useMount(() => {
    const handleResize = () => {
      chartRef.current?.applyOptions({ width: chartElRef.current!.clientWidth, height: chartElRef.current!.clientHeight })
    }

    const chartOptions: DeepPartial<ChartOptions> = {
      layout: {
        textColor: 'black',
        background: { color: 'white' },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: true,
      },
      rightPriceScale: {
        visible: true,
      },
    }

    if (chartElRef.current) {
      const chart = createChart(chartElRef.current, chartOptions)

      const series = chart.addSeries(CandlestickSeries, {
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
      })

      ema5Ref.current = chart.addSeries(LineSeries, { color: '#000000', lineWidth: 1, lineStyle: LineStyle.Solid, title: 'EMA 5' })
      ema10Ref.current = chart.addSeries(LineSeries, { color: '#0000FF', lineWidth: 1, lineStyle: LineStyle.Solid, title: 'EMA 10' })
      ema20Ref.current = chart.addSeries(LineSeries, { color: '#800080', lineWidth: 1, lineStyle: LineStyle.Solid, title: 'EMA 20' })
      sma5Ref.current = chart.addSeries(LineSeries, { color: 'rgba(0, 0, 0, 0.5)', lineWidth: 1, lineStyle: LineStyle.Solid, title: 'SMA 5' })
      sma10Ref.current = chart.addSeries(LineSeries, { color: 'rgba(0, 0, 255, 0.5)', lineWidth: 1, lineStyle: LineStyle.Solid, title: 'SMA 10' })
      sma20Ref.current = chart.addSeries(LineSeries, { color: 'rgba(128, 0, 128, 0.5)', lineWidth: 1, lineStyle: LineStyle.Solid, title: 'SMA 20' })

      chartRef.current = chart
      seriesRef.current = series
      seriesMarkersRef.current = createSeriesMarkers(series, [])

      chart.timeScale().subscribeVisibleTimeRangeChange(function myVisibleTimeRangeChangeHandler(newVisibleTimeRange) {
        if (!newVisibleTimeRange || markersRef.current.length === 0 || !seriesMarkersRef.current) return

        const { from, to } = newVisibleTimeRange
        const visibleMarkers = markersRef.current.filter(({ time }) => time >= from && time <= to)
        seriesMarkersRef.current.setMarkers(visibleMarkers)
      })
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartRef.current) {
        chartRef.current.remove()
        chartRef.current = null
      }
      markersRef.current = []
    }
  })

  useEffect(() => {
    if (!currentCoin) return
    const klineData = getKLineData(currentCoin)
    const latestTime = klineData[klineData.length - 1]?.time
    const startTimeFormatted = latestTime ? dayjs.utc(latestTime * 1000).format('YYYY-MM-DD HH:mm:ss') : undefined
    runFetchKLineData({ symbol: currentCoin, start_time: startTimeFormatted })
  }, [currentCoin, runFetchKLineData, getKLineData])

  return (
    <Loading spinning={loading} wrapperClassName="h-full">
      <div ref={chartElRef} className="w-full h-full relative"></div>
    </Loading>
  )
}
