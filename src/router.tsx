import { createBrowserRouter, Navigate } from 'react-router'
import RootLayout from '@/layouts/root'
import DefaultLayout from '@/layouts/default'
import History from '@/pages/history'
import Position from '@/pages/position'
import Config from '@/pages/config'
import Login from '@/pages/login'

const router = createBrowserRouter([
  {
    Component: RootLayout,
    children: [
      {
        path: '/login',
        Component: Login,
      },
      {
        Component: DefaultLayout,
        children: [
          {
            path: '/',
            element: <Navigate replace to="/history" />,
          },
          {
            path: '/position',
            Component: Position,
          },
          {
            path: '/history',
            Component: History,
          },
          {
            path: '/config',
            Component: Config,
          },
        ],
      },
    ],
  },
])

export default router
