import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'
import { toast } from 'sonner'

import { AUTHORIZATION_KEY } from '@/constants'
import router from '@/router'
import { get } from '.'

export interface ApiResult<T = any> {
  code: number
  data: T
  message: string
}

export interface CacheConfig {
  /**
   * 是否启用缓存
   */
  enableCache?: boolean
  /**
   * 缓存时间，单位毫秒
   */
  cacheTime?: number
  /**
   * 缓存键
   */
  cacheKey?: string
  /**
   * 自定义缓存策略
   * @param cachedData 缓存的数据
   * @returns 返回 true 表示缓存有效，false 表示缓存失效
   */
  cacheStrategy?: (cachedData: any, config: AxiosRequestConfig & CacheConfig) => boolean
}

interface ICacheManager {
  get(key: string): any
}

// 缓存管理器
class CacheManager implements ICacheManager {
  private cache: Map<string, { state: any }> = new Map()

  get(key: string) {
    const namespace = key.split(':')[0]
    const stateKey = key.split(':')[1]

    let state = null
    // if (this.cache.has(namespace)) {
    //   state = this.cache.get(namespace)?.state
    // } else {
    const item = localStorage.getItem(namespace)
    if (!item) return null
    state = JSON.parse(item).state
    // this.cache.set(namespace, { state })
    // }

    return get(state, stateKey)
  }

  set(key: string, value: any) {
    this.cache.set(key, { state: value })
  }
}

const cacheManager = new CacheManager()

const instance = axios.create()

// 请求拦截器
instance.interceptors.request.use((config) => {
  const token = localStorage.getItem(AUTHORIZATION_KEY)
  if (token) {
    config.headers['Authorization'] = token
  }
  return config
})
instance.interceptors.request.use(
  (config) => {
    const cacheConfig = config as AxiosRequestConfig & CacheConfig
    if (cacheConfig.enableCache) {
      const cacheKey = cacheConfig.cacheKey
      const cachedData = cacheManager.get(cacheKey!)

      if (cachedData) {
        // 如果有自定义缓存策略，使用策略判断缓存是否有效
        if (cacheConfig.cacheStrategy) {
          const isValid = cacheConfig.cacheStrategy(cachedData, cacheConfig)
          if (isValid) {
            return Promise.reject({
              __CACHE__: true,
              data: cachedData,
            })
          }
          return config
        } else {
          // 没有自定义策略，使用默认的时间判断
          return Promise.reject({
            __CACHE__: true,
            data: cachedData,
          })
        }
      }
      return config
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 处理缓存命中
    if (error.__CACHE__) {
      return { data: error.data, code: 204 }
    }
    if ([401, 403].includes(error.status)) {
      localStorage.removeItem(AUTHORIZATION_KEY)
      toast.error(error.response?.data?.data || 'Unauthorized')
      router.navigate('/login')
      return Promise.reject(error)
    }
    if (String(error.status).startsWith('5')) {
      toast.error(error.response?.data?.message || 'Server error')
    }
    return Promise.reject(error)
  }
)

// 扩展 axios 实例类型
interface RequestInstance {
  <T = any>(config: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
  <T = any>(url: string, config?: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
  get<T = any>(url: string, config?: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
  delete<T = any>(url: string, config?: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig & CacheConfig): Promise<ApiResult<T>>
}

// 导出缓存管理器，方便手动管理缓存
export { cacheManager }

export default instance as RequestInstance
