/**
 * 根据属性路径获取对象值
 * @param obj 目标对象
 * @param path 属性路径，支持点号分隔的字符串或数组
 * @param defaultValue 默认值，当路径不存在时返回
 * @returns 获取到的值或默认值
 * @example
 * const obj = { a: { b: { c: 1 } } }
 * get(obj, 'a.b.c') // 1
 * get(obj, ['a', 'b', 'c']) // 1
 * get(obj, 'a.b.d', 'default') // 'default'
 */
export function get<T = any>(obj: any, path: string | string[], defaultValue?: T): T | undefined {
  if (!obj || !path) return defaultValue

  const keys = Array.isArray(path) ? path : path.split('.')
  
  return keys.reduce((current, key) => {
    if (current === undefined || current === null) return defaultValue
    return current[key]
  }, obj)
}
