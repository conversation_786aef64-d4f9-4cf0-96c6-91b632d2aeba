import dayjs from 'dayjs'

import { NAMESPACE } from '@/store/position'
import request from '@/utils/request'

// 持仓信息查询
export const fetchPosition = () => {
  return request.post('/api/position/query', {})
}

// k 线查询
export const fetchKLine = (data: { symbol: string; start_time?: string }) => {
  return request.post('/api/kline/query', data, {
    enableCache: false,
    cacheKey: `${NAMESPACE}:klineDataMap.${data.symbol}`,
    cacheStrategy(cachedData) {
      if (!cachedData || cachedData.length === 0) {
        return false
      }
      const latestTime = dayjs.utc(cachedData[0].opening_time)
      return dayjs().subtract(5, 'minute').isBefore(latestTime)
    },
  })
}

// 交易信息
export const fetchTrade = (data: { symbol: string }) => {
  return request.post('/api/position/buy_sell_time', data)
}

// 历史交易信息查询
export const fetchDealOrder = () => {
  return request.post('/api/position/deal_order', {})
}

// 配置信息查询
export const fetchConfig = () => {
  return request.post('/api/config/query', {})
}

// 登录
export const login = (data: { userEmail: string; password: string }) => {
  const { userEmail: user_email, ...rest } = data
  return request.post<{ token: string; user_email: string; user_name: string | null }>('/api/user/login', { user_email, ...rest })
}

// 修改密码
export const resetPassword = (data: { password: string }) => {
  return request.post('/api/user/reset_password', data)
}
