import { cn } from '@/lib/utils'
import { useTranslation } from 'react-i18next'
import { NavLink } from 'react-router'

const MenuItem = ({ to, children }: { to: string; children: React.ReactNode }) => {
  return <NavLink to={to}>{({ isActive }) => <span className={cn('font-medium text-neutral-600', isActive && 'font-semibold text-primary')}>{children}</span>}</NavLink>
}

export default function Menu() {
  const { t } = useTranslation()

  return (
    <nav>
      <ul className="flex items-center gap-4">
        <li>
          <MenuItem to="/history">{t('menu.history')}</MenuItem>
        </li>
        <li>
          <MenuItem to="/position">{t('menu.position')}</MenuItem>
        </li>
        <li>
          <MenuItem to="/config">{t('menu.config')}</MenuItem>
        </li>
      </ul>
    </nav>
  )
}
