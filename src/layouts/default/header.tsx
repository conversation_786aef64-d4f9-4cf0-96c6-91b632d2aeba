import { HandCoins } from 'lucide-react'

import { ModeToggle } from '@/components/mode-toggle'
import { LanguageToggle } from '@/components/language-toggle'
import Menu from './menu'
import UserProfile from '@/components/user-profile'

export default function Header() {
  return (
    <header className="fixed top-0 left-0 right-0 flex items-center justify-between px-6 h-12 bg-background border-b border-zinc-200 dark:border-zinc-800">
      <div className="flex items-center gap-8">
        <h1 className="flex items-center">
          <HandCoins className="w-8 h-8 text-primary" />
          {/* <span className="font-bold">{APP_NAME}</span> */}
        </h1>
        <Menu />
      </div>
      <div className="flex items-center gap-2">
        <ModeToggle />
        <LanguageToggle />
        <UserProfile />
      </div>
    </header>
  )
}
