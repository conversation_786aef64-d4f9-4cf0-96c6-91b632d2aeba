import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { KLineDataMap, KLineUnionData } from '@/types'
import { fetchKLine, fetchPosition } from '@/api'
import dayjs from 'dayjs'

export const NAMESPACE = 'position-storage'

type PositionState = {
  klineDataMap: KLineDataMap
  currentCoin?: string
  positionMap: Record<string, any>
}
type PositionAction = {
  getKLineData: (symbol: string) => KLineUnionData[]
  updateKlineDataMap: (coin: string, klineData: KLineUnionData[]) => void
  fetchKLineData: (params: { symbol: string; start_time?: string }) => Promise<any>
  setCurrentCoin: (coin: string) => void
  setPositionMap: (positionMap: Record<string, any>) => void
  fetchPosition: () => Promise<any>
}

const usePositionStore = create<PositionState & PositionAction>()(
  persist(
    (set, get) => ({
      klineDataMap: {},
      currentCoin: undefined,
      positionMap: {},
      getKLineData: (symbol: string) => get().klineDataMap[symbol] || [],
      updateKlineDataMap: (coin, klineData) => set(({ klineDataMap }) => ({ klineDataMap: { ...klineDataMap, [coin]: klineData } })),
      fetchKLineData: async (params) => {
        const result = await fetchKLine(params)
        if (result.code === 200 && result?.data) {
          const processedNewData: KLineUnionData[] = result.data.map(
            ({ closing_price: close, opening_price: open, highest_price: high, lowest_price: low, opening_time: time, ema_5, ema_10, ema_20, sma_5, sma_10, sma_20 }: any) => {
              return {
                open,
                close,
                low,
                high,
                time: dayjs.utc(time).valueOf() / 1000,
                ema5: ema_5,
                ema10: ema_10,
                ema20: ema_20,
                sma5: sma_5,
                sma10: sma_10,
                sma20: sma_20,
              } as KLineUnionData
            }
          )
          if (params.start_time) {
            // 增量更新
            // [TODO] 限制缓存数据大小
            const cachedData = get().klineDataMap[params.symbol] || []
            const mergedDataMap = new Map<number, KLineUnionData>()
            cachedData.forEach((item) => mergedDataMap.set(item.time, item))
            processedNewData.forEach((item: KLineUnionData) => mergedDataMap.set(item.time, item))
            const mergedData = Array.from(mergedDataMap.values()).sort((a, b) => a.time - b.time)
            get().updateKlineDataMap(params.symbol, mergedData)
          } else {
            // 全量更新
            get().updateKlineDataMap(
              params.symbol,
              processedNewData.sort((a, b) => a.time - b.time)
            )
          }
        }
        return result?.data || []
      },
      setCurrentCoin: (coin) => set({ currentCoin: coin }),
      setPositionMap: (positionMap) => set({ positionMap }),
      fetchPosition: async () => {
        const result = await fetchPosition()
        if (result.code === 200 && result?.data) {
          if (!get().currentCoin) {
            set({ currentCoin: Object.keys(result.data)[0] })
          }
          set({ positionMap: result.data })
        }
        return result?.data || {}
      },
    }),
    { name: NAMESPACE, partialize: (state) => ({ klineDataMap: state.klineDataMap }) }
  )
)

export default usePositionStore
