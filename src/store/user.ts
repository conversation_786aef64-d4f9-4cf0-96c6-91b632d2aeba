import { toast } from 'sonner'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

import type { User } from '@/types'
import { login } from '@/api'
import { AUTHORIZATION_KEY } from '@/constants'

export const NAMESPACE = 'user-storage'

type UserState = {
  user: User | null
}

type UserAction = {
  setUser: (user: User | null) => void
  login: (data: { userEmail: string; password: string }) => Promise<any>
}

const useUserStore = create<UserState & UserAction>()(
  persist(
    (set, get) => ({
      user: null,
      setUser: (user) => set({ user }),
      login: async (data) => {
        const result = await login(data)
        if (result.code === 200 && result?.data) {
          const { user_name, user_email, token } = result.data
          toast.success(`Welcome ${user_name || ''}`)
          localStorage.setItem(AUTHORIZATION_KEY, token)
          get().setUser({ username: user_name, email: user_email })
          return true
        }
        toast.error(result?.message || '登录失败')
        get().setUser(null)
        localStorage.removeItem(AUTHORIZATION_KEY)
        return false
      },
    }),
    { name: NAMESPACE }
  )
)
export default useUserStore
