import { toast } from 'sonner'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

import type { User } from '@/types'
import { login, resetPassword } from '@/api'
import { AUTHORIZATION_KEY } from '@/constants'

export const NAMESPACE = 'user-storage'

type UserState = {
  user: User | null
}

type UserAction = {
  setUser: (user: User | null) => void
  resetPassword: (data: { newPassword: string }) => Promise<any>
  logout: () => void
  login: (data: { userEmail: string; password: string }) => Promise<any>
}

const useUserStore = create<UserState & UserAction>()(
  persist(
    (set, get) => ({
      user: null,
      setUser: (user) => set({ user }),
      resetPassword: async (data: { newPassword: string }) => {
        const result = await resetPassword({ password: data.newPassword })
        if (result.code === 200) {
          toast.success('重置密码成功')
          setTimeout(() => {
            localStorage.removeItem(AUTHORIZATION_KEY)
            window.location.href = '/login'
          }, 1e3)
          return true
        }
        toast.error('重置密码失败')
        return false
      },
      logout: () => {
        localStorage.removeItem(AUTHORIZATION_KEY)
        toast('Welcome back again!')
        setTimeout(() => {
          window.location.href = '/login'
        }, 1e3)
      },
      login: async (data) => {
        const result = await login(data)
        if (result.code === 200 && result?.data) {
          const { user_name, user_email, token } = result.data
          toast.success(`Welcome ${user_name || ''}`)
          localStorage.setItem(AUTHORIZATION_KEY, token)
          get().setUser({ username: user_name, email: user_email })
          return true
        }
        toast.error(result?.message || 'Login failed.')
        get().setUser(null)
        localStorage.removeItem(AUTHORIZATION_KEY)
        return false
      },
    }),
    { name: NAMESPACE }
  )
)
export default useUserStore
