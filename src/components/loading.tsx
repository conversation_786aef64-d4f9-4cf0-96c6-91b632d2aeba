import React from 'react';
import { cn } from '@/lib/utils';

export interface LoadingProps {
  /**
   * 是否显示加载状态
   */
  spinning?: boolean;
  /**
   * 加载提示文案
   */
  tip?: React.ReactNode;
  /**
   * 延迟显示加载效果的时间（防止闪烁）
   */
  delay?: number;
  /**
   * 加载指示符
   */
  indicator?: React.ReactNode;
  /**
   * 组件大小
   */
  size?: 'small' | 'default' | 'large';
  /**
   * 包装器的类名
   */
  wrapperClassName?: string;
  /**
   * 子元素
   */
  children?: React.ReactNode;
}

const Loading: React.FC<LoadingProps> = ({
  spinning = true,
  tip,
  delay = 0,
  indicator,
  size = 'default',
  wrapperClassName,
  children,
}) => {
  const [showLoading, setShowLoading] = React.useState(false);

  React.useEffect(() => {
    if (delay > 0) {
      const timer = setTimeout(() => {
        setShowLoading(spinning);
      }, delay);
      return () => clearTimeout(timer);
    } else {
      setShowLoading(spinning);
    }
  }, [spinning, delay]);

  const defaultIndicator = (
    <div className={cn(
      'animate-spin rounded-full border-4 border-solid border-current border-r-transparent',
      {
        'h-4 w-4': size === 'small',
        'h-8 w-8': size === 'default',
        'h-12 w-12': size === 'large',
      }
    )} />
  );

  const loadingContent = (
    <div className="flex flex-col items-center justify-center">
      {indicator || defaultIndicator}
      {tip && <div className="mt-2 text-sm text-muted-foreground">{tip}</div>}
    </div>
  );

  if (!children) {
    return loadingContent;
  }

  return (
    <div className={cn('relative', wrapperClassName)}>
      {children}
      {showLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-50">
          {loadingContent}
        </div>
      )}
    </div>
  );
};

export default Loading; 
