import { useTranslation } from 'react-i18next'
import { User } from 'lucide-react'

import useUserStore from '@/store/user'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import type { DialogProps } from '@radix-ui/react-dialog'
import { useBoolean, useRequest } from 'ahooks'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

export default function UserProfile() {
  const { t } = useTranslation()
  const [dialogOpen, { setTrue: open, set: setOpen }] = useBoolean(false)
  const user = useUserStore((state) => state.user)
  const logout = useUserStore((state) => state.logout)

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <User />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>
            {user?.username ? (
              <div>
                <div>{user.username}</div>
                <div className="text-xs text-muted-foreground">{user.email}</div>
              </div>
            ) : (
              user?.email
            )}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={open}>
            <span>{t('common.changePassword')}</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={logout}>
            <span className="text-destructive">{t('common.logout')}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ChangePasswordDialog open={dialogOpen} onOpenChange={setOpen} />
    </>
  )
}

const formSchema = z.object({
  password: z.string().min(6, {
    message: 'Password must be at least 6 characters.',
  }),
})

function ChangePasswordDialog({ open, onOpenChange }: DialogProps) {
  const resetPassword = useUserStore((state) => state.resetPassword)

  const { runAsync: resetPsd } = useRequest(resetPassword, {
    manual: true,
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
    },
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const { password } = values
    const success = await resetPsd({ newPassword: password })
    if (success) {
      onOpenChange?.(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>重设密码</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              <div className="grid gap-3">
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input {...field} type="password" placeholder="请输入新密码" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">取消</Button>
                </DialogClose>
                <Button type="submit">确认修改</Button>
              </DialogFooter>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
