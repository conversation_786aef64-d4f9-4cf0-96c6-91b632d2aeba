import { MockMethod } from 'vite-plugin-mock'

export default [
  {
    url: '/api/user/login',
    method: 'post',
    response: {
      code: 200,
      data: {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.wukopHw3H3E5QokJ_SA9rJF_Z7qpHZZwCGN8oIlBoN0',
        user_email: '<EMAIL>',
        user_name: null,
      },
      message: '登录成功',
    },
  },
  {
    url: '/api/position/deal_order',
    method: 'post',
    rawResponse(req, res) {
      const token = req.headers['authorization']
      res.setHeader('Content-Type', 'application/json')
      if (token) {
        res.statusCode = 200
        res.end(
          JSON.stringify({
            code: 200,
            data: [],
          })
        )
      } else {
        res.statusCode = 401
        res.end(
          JSON.stringify({
            code: 401,
            message: '未授权访问',
            data: '认证令牌已过期',
          })
        )
      }
    },
  },
] as MockMethod[]
